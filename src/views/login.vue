<template>
  <div v-if="type == '1'" class="container" >
    <div
      class="login"
      v-if="flag"
      :style="{
        backgroundImage: sysConfig.subjectImage
          ? `url(${sysConfig.subjectImage})`
          : `url(${require('../assets/images/登录页背景.png')})`,
      }"
    >
      <div class="login-box">
        <div class="d-flex j-center">
          <img
            src="../assets/images/logo.png"
            style="width: 112px; height: 50px"
            alt=""
          />
        </div>
        <div class="mr-t15 text">{{ sysConfig.title }}</div>
        <div class="mr-t15 text">{{ sysConfig.abbreviation }}</div>
        <div class="d-flex j-center">
          <img
            src="../assets/images/标题装饰.png"
            style="width: 320px; height: 17px"
            alt=""
          />
        </div>
        <el-form
          ref="loginForm"
          :model="loginForm"
          :rules="loginRules"
          style="width: 100%"
          class="mr-t15"
        >
          <el-form-item prop="username">
            <el-input
              v-model="loginForm.username"
              type="text"
              auto-complete="off"
              placeholder="账号"
              style="width: 100%"
            >
              <img
                slot="prefix"
                src="../assets/images/用户名.png"
                class="mr-t16"
                style="width: 24px; height: 24px"
              />
            </el-input>
          </el-form-item>
          <el-form-item prop="password">
            <el-input
              v-model="loginForm.password"
              type="password"
              auto-complete="off"
              placeholder="密码"
              @keyup.enter.native="handleLogin"
              style="width: 100%"
            >
              <img
                slot="prefix"
                src="../assets/images/密码.png"
                class="mr-t16"
                style="width: 24px; height: 24px"
              />
            </el-input>
          </el-form-item>

          <el-checkbox v-model="loginForm.rememberMe" class="mr-t15"
            >记住密码
          </el-checkbox>
          <el-form-item class="mr-t25">
            <el-button
              :loading="loading"
              type="primary"
              class="loginBtn"
              @click="handleLogin"
              style="width: 100%"
            >
              <span v-if="!loading">登录</span>
              <span v-else>登 录 中...</span>
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <div class="introduce" @click="changeShow('2')">
        <img src="../assets/images/介绍.png" alt="" />
        <span>介绍</span>
      </div>
      <div class="cardLogin" @click="cardLogin">
        <img src="../assets/images/刷卡.png" alt="" />
        <span>刷卡登录</span>
      </div>

      <div class="footer">
        <div>
          <div class="d-flex a-center top">
            <div>{{ sysConfig.p1 }}</div>
            <img src="../assets/images/登录页底部装饰.png" alt="" />

            <div>{{ sysConfig.p2 }}</div>
            <img src="../assets/images/登录页底部装饰.png" alt="" />

            <div>{{ sysConfig.p3 }}</div>
            <img src="../assets/images/登录页底部装饰.png" alt="" />

            <div>{{ sysConfig.p4 }}</div>
          </div>
          <img
            src="../assets/images/登录页底部.png"
            style="width: 916px; height: 46px"
            class="mr-t15"
            alt=""
          />
        </div>
      </div>
    </div>

    <el-dialog
      :visible="visible"
      :close-on-click-modal="false"
      title="提示"
      :show-close="closeFlag"
      @close="visible = false"
    >
      <p style="text-align: center; font-size: 30px; color: #fefefe">请刷卡</p>
    </el-dialog>
  </div>
  <div v-else class="bg" >
    <div class="top">
      <div class="left pd-l20">
        <img
          src="@/assets/images/logo.png"
          style="width: 88px; height: 39px"
          alt=""
        />
        <div>南岭机械厂</div>
      </div>
      <div class="center" v-html="info.info1"></div>
      <div class="right pd-l100">
        <div>{{ time.substring(11) }}</div>
        <div class="divider"></div>
        <div>
          {{ time.substring(0, 10) }}
        </div>
        <div style="margin: 0 10px">星期{{ week }}</div>
      </div>
    </div>
    <div class="bottom">
      <div class="left">
        <div class="notice">
          <img src="@/assets/images/left-arrow.png" alt="" />
          <marquee>{{ notice.content }}</marquee>
          <img src="@/assets/images/right-arrow.png" alt="" />
        </div>
        <div class="flex-1 d-flex j-sb">
          <div class="info">
            <div class="tit d-flex j-center pd-l30 mr-b10">
              <img
                class="before"
                src="../assets/images/介绍标题装饰.png"
                alt=""
              />
              <div>{{ info.info2.title }}</div>
              <img class="zs" src="../assets/images/介绍标题背景.png" alt="" />
            </div>
            <div v-html="info.info2.content" class="pd-l40"></div>
          </div>
          <div class="info pd-t10">
            <div class="tit d-flex j-center pd-l30 mr-b10">
              <img
                class="before"
                src="../assets/images/介绍标题装饰.png"
                alt=""
              />
              <div>{{ info.info3.title }}</div>
              <img class="zs" src="../assets/images/介绍标题背景.png" alt="" />
            </div>
            <div v-html="info.info3.content" class="pd-l40"></div>
          </div>
        </div>
      </div>
      <div class="right">
        <div class="bz">
          <div class="tit d-flex j-center pd-l30 mr-b10">
            <img
              class="before"
              src="../assets/images/介绍标题装饰.png"
              alt=""
            />
            <div>{{ info.info2.title }}</div>
            <img class="zs" src="../assets/images/介绍标题背景.png" alt="" />
          </div>

          <div
            class="list pd-l20 pd-r20 d-flex flex-column j-center"
          >
            <div class="d-flex j-center mr-b10">
              <img
                :src="info.info4[0].url"
                alt=""
              />
            </div>
            <div class="item d-flex j-sb  mr-t20">
              <span class="label">姓名</span>
              <span>{{ info.info4[0].nickName }}</span>
            </div>
            <div class="item d-flex j-sb mr-t20">
              <span class="label">岗位</span>
              <span>{{ info.info4[0].postName }}</span>
            </div>
            <div class="item d-flex j-sb mr-t20">
              <span class="label">编号</span>
              <span>{{ info.info4[0].userNumber }}</span>
            </div>
            <div class="item d-flex j-sb mr-t20">
              <span class="label">部门</span>
              <span>{{ info.info4[0].deptName }}</span>
            </div>
          </div>
        </div>

        <div class="button" @click="changeShow('1')">一键登录</div>
      </div>
    </div>
  </div>
</template>

<script>
import { getSysConfig, getImageBase64, getPublicKey } from "@/api/login";
import Cookies from "js-cookie";
import { encrypt, decrypt } from "@/utils/jsencrypt";
import vueSeamlessScroll from "vue-seamless-scroll";
import axios from "axios";
import moment from "moment";
export default {
  name: "Login",
  components: {
    vueSeamlessScroll,
  },
  data() {
    const checkSilder = (rule, value, callback) => {
      if (!this.$refs.silderVerifyRef.confirmSuccess) {
        return callback(new Error("请完成滑动的拖动"));
      }
      callback();
    };

    return {
      cookiePassword: "",
      loginForm: {
        username: "",
        password: "",
        rememberMe: false,
      },
      loginRules: {
        username: [
          { required: true, trigger: "blur", message: "请输入您的账号" },
        ],
        password: [
          { required: true, trigger: "blur", message: "请输入您的密码" },
        ],
        isSilder: [{ validator: checkSilder }],
      },
      loading: false,
      // // 验证码开关
      // captchaOnOff: true,
      // 注册开关
      register: false,
      silderVerify: false,
      redirect: undefined,
      sysConfig: {
        subjectImage: "",
        p1: "透明化工厂",
        p2: "精益制造",
        copyright: "",
        p3: "数字化管控",
        p4: "降本增效",
        logo: "",
        title: "",
        abbreviation: "",
        url: "",
        docTitle: "",
      },
      visible: false,
      closeFlag: false,
      flag: true, // 默认显示登录框，避免API调用失败时无法显示
      type: "1",
      info: {},
      loginErrorCount: 0,
      noticeList: [],
      notice: "",
      pageNum: 1, // 当前页码
      pageSize: 10, // 每页的数量
      total: 0,
      time: "",
      week: "",
      timer: null,
      currentIndex: 0,
        x: 1,
      y: 1,
    };
  },
  computed: {
    defaultOption() {
      return {
        step: 1, // 数值越大速度滚动越快
        limitMoveNum: this.info.info4 ? this.info.info4.length : 20, // 开始无缝滚动的数据量 this.dataList.length
        hoverStop: true, // 是否开启鼠标悬停stop
        direction: 1, // 0向下 1向上 2向左 3向右
        openWatch: true, // 开启数据实时监控刷新dom
        singleHeight: 0, // 单步运动停止的高度(默认值0是无缝不停止的滚动) direction => 0/1
        singleWidth: 0, // 单步运动停止的宽度(默认值0是无缝不停止的滚动) direction => 2/3
        waitTime: 1000, // 单步运动停止的时间(默认值1000ms)
      };
    },
  },
  watch: {
    $route: {
      handler: function (route) {
        this.redirect = route.query && route.query.redirect;
        if (route.query.type) {
          sessionStorage.setItem("loginType", route.query.type);
        }
      },
      immediate: true,
    },
    "sysConfig.docTitle"() {
      document.title = this.sysConfig.docTitle;
      sessionStorage.setItem("docTitle", this.sysConfig.docTitle);
    },
  },
  created() {
    this.getCookie();
    // this.resize();
    // window.onresize = this.resize;
    let type = sessionStorage.getItem("loginType") || "1";
    getSysConfig({
      type,
    })
      .then((res) => {
        // 可能返回空对象
        this.flag = true;
        if (
          res.data == null ||
          Object.getOwnPropertyNames(res.data).length == 0
        ) {
          return;
        }

        this.$store.dispatch('LogOut').then(() => {
          //location.href = '/index';
        });

        this.sysConfig = res.data;

        sessionStorage.setItem("docTitle", this.sysConfig.docTitle || "");

        this.sysConfig.logoId = this.sysConfig.logo;
        this.sysConfig.subjectImageId = this.sysConfig.subjectImage;

        // 从缓存中取
        let imgCache = localStorage.getItem("imgCache");
        imgCache = imgCache ? JSON.parse(imgCache) : {};

        const { logoId, subjectImageId } = this.sysConfig;

        for (const key in imgCache) {
          if (![logoId, subjectImageId].includes(key)) {
            delete imgCache[key];
          }
        }

        if (logoId) {
          // 如果没有则进行存储
          if (imgCache[logoId]) {
            this.sysConfig.logo = imgCache[logoId];
          } else {
            getImageBase64(logoId).then((res) => {
              this.blobToDataURI(res, (base64) => {
                imgCache[logoId] = base64;
                this.sysConfig.logo = base64;
                localStorage.setItem("imgCache", JSON.stringify(imgCache));
              });
            });
          }
        }

        if (subjectImageId) {
          if (imgCache[subjectImageId]) {
            this.sysConfig.subjectImage = imgCache[subjectImageId];
          } else {
            getImageBase64(subjectImageId).then((res) => {
              this.blobToDataURI(res, (base64) => {
                imgCache[subjectImageId] = base64;
                this.sysConfig.subjectImage = base64;
                localStorage.setItem("imgCache", JSON.stringify(imgCache));
              });
            });
          }
        }
      })
      .catch(() => {
        this.flag = true; // 确保即使API调用失败也显示登录框
        this.sysConfig = {
          subjectImage: require("@/assets/images/detail.png"),
          p1: "快捷高效",
          p2: "管理科学",
          copyright: "",
          p3: "精准定位",
          p4: "增产提效",
          logo: "",
          title: "数字化修理线",
          abbreviation: "",
          url: "",
          docTitle: "数字化修理线",
        };
      });
    if (!this.info.backgroundImage) {
      this.$api({
        url: "/interfaces/info/get",
      }).then((res) => {
        this.info = res.data;
        if (this.info.workStation == 0) {
          this.type = "1";
        } else if (this.info.workStation == 1) {
          this.type = "2";
        } else if (this.info.workStation == 2 && this.info.url) {
          let timer = setTimeout(() => {
            location.href = this.info.url;
          }, 5000);
          this.$confirm("是否跳转页面?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
            closeOnClickModal: false,
          })
            .then(() => {
              location.href = this.info.url;
            })
            .catch(() => {
              clearTimeout(timer);
            });
        }
        this.$once("hook:beforeDestroy", () => {
          clearInterval(this.timer);
        });
      }).catch((error) => {
        // API调用失败时，确保type保持为"1"以显示登录框
        console.warn("获取工位信息失败，使用默认登录模式:", error);
        this.type = "1";
      });
    }
    this.getNotice();
    this.getTime();
    let timer = setInterval(() => {
      this.getNotice();
    }, 120000);
    this.$once("hook:beforeDestroy", () => {
      clearInterval(timer);
    });
  },
  methods: {
    resize() {
      this.x = document.body.clientWidth / 1920;
      this.y = document.body.clientHeight / 1080;
    },
    getTime() {
      const timer = setInterval(() => {
        this.time = moment(new Date()).format("yyyy-MM-DD HH:mm:ss");
        this.week = ["日", "一", "二", "三", "四", "五", "六"][
          new Date().getDay()
        ];
        // 定时器操作
      }, 1000);

      // 通过$once来监听定时器，在beforeDestroy钩子可以被清除。
      this.$once("hook:beforeDestroy", () => {
        clearInterval(timer);
      });
    },
    getNotice() {
      this.$api({
        url: "/interfaces/info/getNotice",
        params: {
          pageNum: this.pageNum,
          pageSize: this.pageSize,
        },
      }).then((res) => {
        this.noticeList = res.data.records;
        this.total = res.data.total;
        if (!this.notice) {
          this.notice = this.noticeList[0];
        }
      });
    },
    changeShow(type) {
      this.type = type;
    },
    cardLogin() {
      this.visible = true;
      this.closeFlag = false;
      let timer = setTimeout(() => {
        this.closeFlag = true;
      }, 15000);
      axios
        .get(
          `http://${
            this.$store.state.permission.requestUrl
          }:18980/login?loginCode=${Math.random()}`
        )
        .then((res) => {
          clearTimeout(timer);
          if (res.data.code == 200) {
            const type = this.$route.query.type;
            if (type !== undefined) {
              res.data.data.type = type;
            }

            this.$store.dispatch("cardLogin", res.data.data).then(() => {
              this.$router.push({ path: "/workStation" });
            });
          } else {
            this.$message.warning(res.data.msg);
            this.visible = false;
          }
        });
    },

    blobToDataURI(blob, callback) {
      var reader = new FileReader();
      reader.readAsDataURL(blob);
      reader.onload = function (e) {
        callback(e.target.result);
      };
    },
    getCookie() {
      const username = Cookies.get("username");
      const password = Cookies.get("password");
      const rememberMe = Cookies.get("rememberMe");
      this.loginForm = {
        username: username === undefined ? this.loginForm.username : username,
        password:
          password === undefined ? this.loginForm.password : decrypt(password),
        rememberMe: rememberMe === undefined ? false : Boolean(rememberMe),
      };
    },
    handleLogin() {
      this.$refs.loginForm.validate((valid) => {
        if (valid) {
          this.loading = true;
          const type = this.$route.query.type;
          console.log("type-->"+type)
          if (type !== undefined) {
            this.loginForm["type"] = type;
            Cookies.set("type", type, { expires: 30 });
          }
          if (this.loginForm.rememberMe) {
            Cookies.set("username", this.loginForm.username, { expires: 30 });
            Cookies.set("password", encrypt(this.loginForm.password), {
              expires: 30,
            });
            Cookies.set("rememberMe", this.loginForm.rememberMe, {
              expires: 30,
            });
          } else {
            Cookies.remove("username");
            Cookies.remove("password");
            Cookies.remove("rememberMe");
          }
          getPublicKey(1, this.loginForm.username)
            .then((res) => {
              let isLoging = res.data.isLogin;
              let publicKey = res.data.publicKey;
              if (isLoging) {
                this.$confirm("此账号已在线，是否继续登录?", "提示", {
                  confirmButtonText: "确定",
                  cancelButtonText: "取消",
                  type: "warning",
                })
                  .then(() => {
                    this.loginIng(publicKey);
                  })
                  .catch(() => {
                    this.loading = false;
                  });
              } else {
                this.loginIng(publicKey);
              }
            })
            .catch(() => {
              this.loading = false;
              ++this.loginErrorCount;
              if (this.loginErrorCount >= 2) {
                this.silderVerify = true;
              }
              this.$refs.silderVerifyRef.reset();
            });
        }
      });
    },
    loginIng(publicKey) {
      this.loginForm["publicKey"] = publicKey;
      this.$store
        .dispatch("Login", this.loginForm)

        .then(() => {
          this.$router.push({ path: "/workStation" });
        })
        .catch(() => {
          this.loading = false;
          ++this.loginErrorCount;
          if (this.loginErrorCount >= 2) {
            this.silderVerify = true;
          }
          this.$refs.silderVerifyRef.reset();
        });
    },
    jump() {
      if (this.sysConfig.url) window.open(this.sysConfig.url, "href");
    },
  },
};
</script>

<style  lang="scss" scoped>
.container {
  height: 100%;
  background: linear-gradient(0deg, #0048c0, #00307f);
  .login {
    height: 100%;
    position: relative;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    color: #fefefe;
    .login-box {
      position: absolute;
      right: 290px;
      top: 207px;
      width: 500px;
      height: 566px;
      background: url("../assets/images/登录框.png") 100% 100% no-repeat;
      padding: 38px 56px;
      ::v-deep .el-input__inner {
        height: 56px;
        font-size: 16px;
        padding-left: 40px;
      }
      ::v-deep .el-checkbox__label {
        font-size: 18px;

        color: #ffffff;
        line-height: 40px;

        background: linear-gradient(0deg, #cef1ff 0%, #ffffff 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      ::v-deep .el-checkbox__inner {
        width: 20px;
        height: 20px;
        transform: translateY(-1px);
        background: transparent;
        &::after {
          width: 6px;
          height: 12px;
          left: 6px;
        }
      }
      .text {
        font-size: 28px;
        font-weight: bold;
        color: #ffffff;
        line-height: 40px;
        text-align: center;
        background: linear-gradient(0deg, #aed8ff 0%, #ffffff 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      .loginBtn {
        height: 54px;
        background: linear-gradient(180deg, #0099ff, #0076ff) !important;
        border-radius: 5px;
        font-size: 20px;
        outline: none;
      }
    }
    .introduce,
    .cardLogin {
      position: absolute;
      display: flex;
      flex-direction: column;
      align-items: center;
      background: url("../assets/images/文字框.png") 100% 100% no-repeat;
      background-size: 100% 100%;
      padding-top: 5px;
      width: 98px;
      height: 98px;
      cursor: pointer;
      & > img {
        width: 48px;
        height: 48px;
        margin-bottom: 5px;
      }
      & > span {
        font-size: 16px;

        color: #ffffff;
        -webkit-text-stroke: 1px #ffffff;
        text-stroke: 1px #ffffff;

        background: linear-gradient(135deg, #ffffff 0%, #31aaff 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }
    .introduce {
      right: 796px;
      top: 551px;
    }
    .cardLogin {
      right: 796px;
      top: 658px;
    }
    .footer {
      position: absolute;
      bottom: 0;
      padding-bottom: 10px;
      display: flex;
      justify-content: center;
      width: 100%;
      .top {
        justify-content: center;
        & > div {
          font-size: 20px;
          line-height: 24px;
          color: #ffffff;

          background: linear-gradient(0deg, #cef1ff 0%, #ffffff 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }

        & > img {
          width: 24px;
          height: 24px;
          margin: 0 15px;
          transform: translateY(3px);
        }
      }
    }
  }
}

.bg {
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  background-image: url("../assets/images/主页背景.png");
  background-size: 100% 100%;
  background-repeat: no-repeat;
  color: #fff;
  .top {
    background: url("../assets/images/介绍头部.png") no-repeat;
    background-size: 100% 100%;
    height: 121px;
    display: flex;
    justify-content: space-between;
    .left,
    .right {
      width: 400px;
      display: flex;
      align-items: center;
      padding: 0 5px;
      font-size: 20px;
      height: 60px;
      color: #ffffff;
      line-height: 60px;
      text-shadow: 0px 5px 2px rgba(0, 49, 87, 0.1);
      background: linear-gradient(0deg, #8ad0ff 0%, #ffffff 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .center {
      width: 400px;
      text-align: center;
      font-size: 36px;
      font-weight: bold;
      color: #ffffff;
      line-height: 60px;
      text-shadow: 0px 4px 4px rgba(0, 25, 87, 0.1);
      background: linear-gradient(0deg, #8ad0ff 0%, #ffffff 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .divider {
      width: 1px;
      height: 16px;
      background: #ffffff;
      opacity: 0.78;
      margin: 0 8px;
    }
  }
  .bottom {
    padding: 0 20px 20px 20px;
    height: calc(100% - 121px);
    display: flex;
    justify-content: space-between;
    .left {
      width: 75%;
      display: flex;
      flex-direction: column;
      .notice {
        height: 90px;
        background: url("../assets/images/通知背景.png") no-repeat;
        background-size: 100% 100%;
        marquee {
          font-size: 32px;
          font-weight: bold;
          color: #8cd4ff;
        }

        display: flex;
        align-items: center;

        padding: 5px;
        margin-bottom: 10px;
        img {
          width: 42px;
          height: 64px;
        }
      }
      .info {
        width: 49.5%;
        height: 100%;
        border-radius: 5px;
        font-size: 28px;
        line-height: 35px;
        letter-spacing: 3px;
        background: url("../assets/images/介绍背景.png") no-repeat;
        background-size: 100% 100%;
        padding: 10px;
        overflow: auto;
      }
    }
    .right {
      width: 24%;
      height: 100%;

      .bz {
        height: calc(100% - 80px);
        background: url("../assets/images/班组成员背景.png") no-repeat;
        background-size: 100% 100%;
        padding-top: 20px;
      }
      .list {
        overflow: hidden;
        height: calc(100% - 100px);
        img{
          width: 92px;
          height: 92px;
          border-radius: 50%;
        }
        .item{
          font-size: 18px;
           padding-bottom: 20px;
           border-bottom: 1px dotted rgba($color: #fff, $alpha: 0.3);
           .label{
            color: rgba($color: #fff, $alpha: 0.7);
           }
        }
      }
      .button {
        height: 70px;
        width: 100%;
        margin-top: 10px;
        background: url("../assets/images/一键登录.png") no-repeat;
        background-size: 100% 100%;
        line-height: 70px;
        text-align: center;
        font-size: 24px;
        cursor: pointer;
        color: #d4edff;
      }
    }

    .tit {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      height: 65px;
      font-size: 22px;
      font-weight: bold;
      color: #d4edff;
      line-height: 30px;
      text-shadow: 0px 2px 2px rgba(3, 55, 121, 0.2);
      .before {
        width: 35px;
        height: 35px;
      }
      .zs {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 34px;
      }
    }
  }
  .person {
    display: flex;
    width: 100%;
    align-items: center;
    justify-content: space-between;
    padding: 0 10px;
    font-size: 20px;
    margin: 10px 0;
  }
}
</style>
