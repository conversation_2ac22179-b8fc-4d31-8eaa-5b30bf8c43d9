<template>
  <magic-editor :config="config"/>
</template>

<script>
  // 引入组件
  import MagicEditor from '@/magic-editor'
  // 引入样式
  import '@/magic-editor/assets/magic-editor.css'
  export default {
    components: {
      MagicEditor
    },
    data(){
      return {
        config:{
          baseURL: process.env.VUE_APP_BASE_API+'/interfaces/magic/web',    //配置后台服务
          serverURL: process.env.VUE_APP_BASE_API+'/interfaces',   //配置接口实际路径
          inJar: true,
          // 添加调试配置
          autoSave: true,
          blockClose: false,
          // 添加请求和响应拦截器配置
          request: {
            beforeSend: (config) => {
              console.log('Magic API Request:', config);
              return config;
            },
            onError: (err) => {
              console.error('Magic API Request Error:', err);
              return Promise.reject(err);
            }
          },
          response: {
            onSuccess: (resp) => {
              console.log('Magic API Response:', resp);
              return resp;
            },
            onError: (err) => {
              console.error('Magic API Response Error:', err);
              return Promise.reject(err);
            }
          }
        }
      }
    }
  }
</script>
